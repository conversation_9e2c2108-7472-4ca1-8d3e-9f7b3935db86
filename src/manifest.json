{"name": "省赚客", "appid": "__UNI__09CEBDB", "description": "", "versionName": "1.5.8", "versionCode": "158", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": false, "autoclose": true, "delay": 1}, "modules": {"Share": {}, "Geolocation": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\"/>", "<uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>"], "enableResourceOptimizations": false, "targetSdkVersion": 30, "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {"urltypes": [{"urlschemes": ["tbopen34494988"]}], "urlschemewhitelist": "tbopen,tmall", "deploymentTarget": "11.0", "validArchitectures": ["arm64", "x86_64"], "enableResourceOptimizations": false}, "sdkConfigs": {"share": {"weixin": {"appid": "wxd8986180cb8a93ee"}}, "geolocation": {"system": {"__platform__": ["ios", "android"]}}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "splashscreen": {"androidStyle": "default", "android": {"hdpi": "unpackage/res/splash/advert_480_762.9.png", "xhdpi": "unpackage/res/splash/advert_720_1242.9.png", "xxhdpi": "unpackage/res/splash/advert_1080_1882.9.png"}}}, "nativePlugins": {"UZK-Alibcsdk": {"__plugin_info__": {"name": "新版阿里百川SDK安卓V4.1.0.1苹果V4.1.0.0", "description": "新增渠道授权（静默式），无需用户进行二次授权，即可获得用户的渠道ID。", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=873", "android_package_name": "cn.juwatech.szk", "ios_bundle_id": "cn.juwatech.szk", "isCloud": true, "bought": 1, "pid": "873", "parameters": {}}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "darkmode": true, "themeLocation": "theme.json"}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"darkmode": true, "themeLocation": "theme.json"}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3"}